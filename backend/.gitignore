# ============================================================================
# GameFlex Backend .gitignore
#
# This file excludes temporary files, dependencies, and runtime data while
# keeping essential configuration and source files tracked in git.
#
# TRACKED FILES INCLUDE:
# - Source code (*.js, *.py, *.ts, etc.)
# - Configuration templates (wrangler.toml, package.json, etc.)
# - Documentation (README.md, *.md)
# - Asset files (assets/media/*.jpg, *.webp)
# - Scripts (scripts/*.sh)
# - Docker configuration (docker-compose.yml, Dockerfile)
#
# IGNORED FILES INCLUDE:
# - Dependencies (node_modules/, .aws-sam/)
# - Runtime data (storage/, tmp/, logs/)
# - Environment files (.env, .env.local)
# - Generated files (package-lock.json, *.log)
# ============================================================================

# AWS SAM specific
.aws-sam/
samconfig.toml
packaged.yaml
packaged-template.yaml

# ============================================================================
# Node.js and Package Managers
# ============================================================================
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock
.pnpm-debug.log

# Lambda deployment packages
*.zip

# ============================================================================
# Environment Variables and Configuration
# ============================================================================
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Build output
dist/
build/
.build/
out/

# Coverage reports
coverage/
.nyc_output/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# VSCode settings
.vscode/

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
out/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# SAM local API
.sam_pid

# Temporary files
*.tmp
*.temp
temp/
tmp/

# CloudFormation outputs
cfn-outputs.json

# AWS CDK
cdk.out/

# Local DynamoDB
.dynamodb/

# Swagger
swagger.yaml
swagger.json

# Backup files
*.bak
*.backup
*~

# Debug files
.debug/
debug/

# Test files
.test/
test-results/

# ============================================================================
# GameFlex Development Environment
# ============================================================================

# Local development
volume/

# LocalStack specific
.localstack/
localstack_data/
localstack-data/

# Docker volumes and temporary data
tmp/
.tmp/
docker-data/

# Cloudflare R2 Simulator
cloudflare/node_modules/
cloudflare/package-lock.json
cloudflare/yarn.lock
cloudflare/.npm/
cloudflare/.cache/

# R2 Simulator storage (uploaded files during development)
cloudflare/storage/
tmp/cloudflare/

# Environment files (keep templates, ignore actual config)
.env.localstack
.env.cloudflare
.env.r2

# Docker Compose override files
docker-compose.override.yml
docker-compose.local.yml

# Development databases and storage
*.db
*.sqlite
*.sqlite3

# Process IDs and lock files
*.pid
*.lock

# Development certificates
*.pem
*.key
*.crt
*.cert

# Local configuration overrides
config.local.json
settings.local.json

# Test artifacts
test-output/
test-results/
coverage-reports/

# Backup and temporary files
*.backup
*.bkp
*.orig
*.swp
*.swo
*~

# OS generated files
.DS_Store?
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db

# Editor temporary files
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.vscode/extensions.json
*.sublime-*
.brackets.json

# Runtime and cache directories
.cache/
.temp/
.tmp/
runtime/
cache/

# Development logs
*.dev.log
dev-*.log
development.log

# Local scripts (if any sensitive ones are created)
scripts/local-*
scripts/*.local.*

# Wrangler specific (if using real Cloudflare later)
.wrangler/
wrangler.toml.local

# Docker development
.docker/
docker-compose.dev.yml

# ============================================================================
# IMPORTANT: Files that SHOULD be tracked
# ============================================================================
# The following files should remain tracked in git:
#
# Essential source files:
# !assets/media/cod_screenshot.jpg
# !assets/media/diablo_screenshot.webp
# !cloudflare/server.js
# !cloudflare/package.json
# !cloudflare/README.md
# !cloudflare/wrangler.toml
# !scripts/*.sh
# !docker-compose.yml
# !*.md
#
# Note: These are tracked by default unless explicitly ignored above
